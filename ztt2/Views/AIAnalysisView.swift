//
//  AIAnalysisView.swift
//  ztt2
//
//  Created by Augment Agent on 2025/8/1.
//

import SwiftUI

/**
 * AI分析报告主视图
 * 参考ztt1项目实现，负责展示成员的AI行为分析报告，包含权限检查、加载状态和报告内容
 */
struct AIAnalysisView: View {

    // MARK: - Properties
    let member: Member
    let onDismiss: () -> Void
    let onNavigateToSubscription: (() -> Void)?

    // MARK: - State
    @StateObject private var viewModel = AIAnalysisViewModel()
    @State private var pageAppeared = false
    @State private var selectedReportType: AIReportType = .behaviorAnalysis

    // MARK: - Body
    var body: some View {
        GeometryReader { geometry in
            ZStack {
                // 背景渐变 - 占满整个屏幕
                createBackgroundGradient()

                // 主要内容 - 使用ScrollView确保内容可滚动
                ScrollView {
                    VStack(spacing: 0) {
                        // 顶部间距，为导航栏留空间
                        Color.clear
                            .frame(height: max(geometry.safeAreaInsets.top, 44) + 60)

                        // 主要内容区域
                        if viewModel.canAccessAIAnalysisPage(for: member).canAccess {
                            if let report = viewModel.currentReport {
                                // 显示报告内容
                                reportContentView(report: report)
                            } else if viewModel.isGenerating {
                                // 加载状态
                                loadingView
                                    .frame(height: geometry.size.height - max(geometry.safeAreaInsets.top, 44) - 120)
                            } else {
                                // 生成报告按钮
                                generateReportView
                            }
                        } else {
                            // 权限不足视图（仅针对非高级会员）
                            permissionDeniedView
                                .frame(height: geometry.size.height - max(geometry.safeAreaInsets.top, 44) - 120)
                        }

                        // 底部安全区域
                        Color.clear
                            .frame(height: geometry.safeAreaInsets.bottom + 20)
                    }
                }

                // 顶部导航栏 - 浮动在最上层
                VStack {
                    topNavigationBar
                        .padding(.top, max(geometry.safeAreaInsets.top, 44))
                    Spacer()
                }
            }
        }
        .navigationBarHidden(true)
        .ignoresSafeArea()
        .onAppear {
            setupView()
        }
        .alert("错误", isPresented: .constant(viewModel.errorMessage != nil)) {
            Button("确定") {
                viewModel.clearError()
            }
        } message: {
            if let errorMessage = viewModel.errorMessage {
                Text(errorMessage)
            }
        }
    }

    // MARK: - View Components

    /**
     * 顶部导航栏
     * 修复：调整布局，确保在安全区域内正确显示
     */
    private var topNavigationBar: some View {
        HStack {
            // 返回按钮
            Button(action: {
                onDismiss()
            }) {
                HStack(spacing: 6) {
                    Image(systemName: "chevron.left")
                        .font(.system(size: 16, weight: .medium))
                    Text("ai_analysis.return".localized)
                        .font(.system(size: 16))
                }
                .foregroundColor(DesignSystem.Colors.textPrimary)
            }
            .frame(width: 80, height: 44, alignment: .leading)

            Spacer()

            // 标题
            Text("ai_analysis.title".localized)
                .font(.system(size: 18, weight: .semibold))
                .foregroundColor(DesignSystem.Colors.textPrimary)

            Spacer()

            // 右侧占位（保持对称）
            Color.clear
                .frame(width: 80, height: 44)
        }
        .padding(.horizontal, 20)
        .frame(height: 60)
        .background(Color.clear)
        .opacity(pageAppeared ? 1.0 : 0.0)
        .animation(.easeInOut(duration: 0.6), value: pageAppeared)
    }

    /**
     * 背景渐变
     * 参考ztt1项目的多层渐变效果，创造更自然的视觉过渡
     */
    private func createBackgroundGradient() -> some View {
        LinearGradient(
            gradient: Gradient(stops: [
                .init(color: Color(hex: "#fcfff4"), location: 0.0),
                .init(color: Color(hex: "#f8fdf0"), location: 0.3),
                .init(color: Color.white, location: 0.7),
                .init(color: Color(hex: "#fafffe"), location: 1.0)
            ]),
            startPoint: .top,
            endPoint: .bottom
        )
        .ignoresSafeArea(.all)
    }

    /**
     * 权限不足视图
     */
    private var permissionDeniedView: some View {
        VStack(spacing: 24) {
            Spacer()

            // 权限图标
            Image(systemName: "lock.circle")
                .font(.system(size: 64))
                .foregroundColor(Color(hex: "#FFB84D"))
                .opacity(pageAppeared ? 1.0 : 0.0)
                .scaleEffect(pageAppeared ? 1.0 : 0.8)
                .animation(.spring(response: 0.8, dampingFraction: 0.6).delay(0.2), value: pageAppeared)

            // 权限说明
            VStack(spacing: 12) {
                Text("ai_analysis.permission_title".localized)
                    .font(.system(size: 20, weight: .semibold))
                    .foregroundColor(DesignSystem.Colors.textPrimary)

                Text(getPermissionStatusDescription())
                    .font(.system(size: 16))
                    .foregroundColor(DesignSystem.Colors.textSecondary)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal, 32)
            }
            .opacity(pageAppeared ? 1.0 : 0.0)
            .offset(y: pageAppeared ? 0 : 20)
            .animation(.easeOut(duration: 0.6).delay(0.4), value: pageAppeared)

            // 升级按钮
            if let onNavigateToSubscription = onNavigateToSubscription {
                Button(action: {
                    onNavigateToSubscription()
                }) {
                    HStack {
                        Image(systemName: "crown.fill")
                            .font(.system(size: 16))
                        Text("升级会员")
                            .font(.system(size: 16, weight: .medium))
                    }
                    .foregroundColor(.white)
                    .padding(.horizontal, 32)
                    .padding(.vertical, 16)
                    .background(
                        LinearGradient(
                            gradient: Gradient(colors: [
                                Color(hex: "#FFB84D"),
                                Color(hex: "#FF9500")
                            ]),
                            startPoint: .leading,
                            endPoint: .trailing
                        )
                    )
                    .cornerRadius(25)
                    .shadow(color: Color(hex: "#FFB84D").opacity(0.3), radius: 8, x: 0, y: 4)
                }
                .opacity(pageAppeared ? 1.0 : 0.0)
                .offset(y: pageAppeared ? 0 : 20)
                .animation(.easeOut(duration: 0.6).delay(0.6), value: pageAppeared)
            }

            Spacer()
        }
        .padding(.horizontal, 24)
    }

    /**
     * 生成报告视图
     * 修复：调整布局，确保内容适配屏幕
     */
    private var generateReportView: some View {
        VStack(spacing: 20) {
            // 成员信息卡片
            memberInfoCard

            // 报告类型选择器
            reportTypeSelector

            // 生成按钮 - 优化设计
            Button(action: {
                // 检查是否可以生成报告
                let canGenerate = viewModel.canGenerateReport(for: member, reportType: selectedReportType)
                if canGenerate.canGenerate {
                    Task {
                        await generateSelectedReport()
                    }
                } else {
                    // 显示数据不足的弹窗提示
                    viewModel.errorMessage = canGenerate.reason
                }
            }) {
                HStack(spacing: 12) {
                    Image(systemName: "brain.head.profile")
                        .font(.system(size: 20, weight: .medium))
                    Text("ai_analysis.generate_button".localized)
                        .font(.system(size: 17, weight: .semibold))
                }
                .foregroundColor(.white)
                .padding(.horizontal, 40)
                .padding(.vertical, 18)
                .background(
                    ZStack {
                        // 主渐变背景
                        LinearGradient(
                            gradient: Gradient(colors: [
                                DesignSystem.Colors.primary,
                                Color(hex: "#74c07f")
                            ]),
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )

                        // 高光效果
                        LinearGradient(
                            gradient: Gradient(colors: [
                                Color.white.opacity(0.3),
                                Color.clear
                            ]),
                            startPoint: .top,
                            endPoint: .center
                        )
                    }
                )
                .cornerRadius(28)
                .shadow(color: DesignSystem.Colors.primary.opacity(0.4), radius: 12, x: 0, y: 6)
                .shadow(color: .black.opacity(0.1), radius: 2, x: 0, y: 1)
            }
            .disabled(!viewModel.isNetworkAvailable)
            .opacity(viewModel.isNetworkAvailable ? 1.0 : 0.6)

            // 网络状态提示
            if !viewModel.isNetworkAvailable {
                Text("ai_analysis.no_network_connection".localized)
                    .font(.system(size: 14))
                    .foregroundColor(DesignSystem.Colors.errorColor)
            }

            // 历史分析记录
            historyReportsSection
        }
        .padding(.horizontal, 24)
        .opacity(pageAppeared ? 1.0 : 0.0)
        .offset(y: pageAppeared ? 0 : 30)
        .animation(.easeOut(duration: 0.8).delay(0.3), value: pageAppeared)
    }

    /**
     * 成员信息卡片
     * 优化：增强视觉层次，添加渐变背景，改进数据展示
     */
    private var memberInfoCard: some View {
        VStack(spacing: 20) {
            // 头像和基本信息
            HStack(spacing: 16) {
                // 头像容器 - 添加渐变边框效果
                ZStack {
                    Circle()
                        .fill(
                            LinearGradient(
                                gradient: Gradient(colors: [
                                    DesignSystem.Colors.primary.opacity(0.3),
                                    DesignSystem.Colors.secondary.opacity(0.2)
                                ]),
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .frame(width: 68, height: 68)

                    Image(member.avatarImageName)
                        .resizable()
                        .aspectRatio(contentMode: .fit)
                        .frame(width: 60, height: 60)
                        .clipShape(Circle())
                }

                VStack(alignment: .leading, spacing: 6) {
                    Text(member.displayName)
                        .font(.system(size: 22, weight: .bold))
                        .foregroundColor(DesignSystem.Colors.textPrimary)

                    Text("\(member.roleDisplayName) · \(member.age)岁")
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                }

                Spacer()
            }

            // 数据统计 - 改进布局和视觉效果
            HStack(spacing: 0) {
                dataStatItem(
                    title: "行为记录",
                    count: member.sortedPointRecords.count,
                    minRequired: 10
                )
                .frame(maxWidth: .infinity)

                // 分隔线 - 使用渐变效果
                Rectangle()
                    .fill(
                        LinearGradient(
                            gradient: Gradient(colors: [
                                DesignSystem.Colors.textTertiary.opacity(0.3),
                                DesignSystem.Colors.textTertiary.opacity(0.8),
                                DesignSystem.Colors.textTertiary.opacity(0.3)
                            ]),
                            startPoint: .top,
                            endPoint: .bottom
                        )
                    )
                    .frame(width: 1, height: 50)

                dataStatItem(
                    title: "成长日记",
                    count: member.sortedGrowthDiaries.count,
                    minRequired: 10
                )
                .frame(maxWidth: .infinity)
            }
        }
        .padding(24)
        .background(
            // 使用渐变背景增强视觉效果
            RoundedRectangle(cornerRadius: 20)
                .fill(
                    LinearGradient(
                        gradient: Gradient(colors: [
                            Color.white,
                            Color(hex: "#fcfff4").opacity(0.5)
                        ]),
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
                .shadow(color: DesignSystem.Colors.primary.opacity(0.15), radius: 12, x: 0, y: 4)
                .shadow(color: .black.opacity(0.05), radius: 2, x: 0, y: 1)
        )
        .opacity(pageAppeared ? 1.0 : 0.0)
        .scaleEffect(pageAppeared ? 1.0 : 0.95)
        .animation(.spring(response: 0.6, dampingFraction: 0.8).delay(0.2), value: pageAppeared)
    }

    /**
     * 数据统计项
     * 优化：增强视觉层次，添加状态指示器
     */
    private func dataStatItem(title: String, count: Int, minRequired: Int) -> some View {
        VStack(spacing: 10) {
            // 数字显示 - 添加状态指示器
            ZStack {
                // 背景圆圈
                Circle()
                    .fill(count >= minRequired ? DesignSystem.Colors.primary.opacity(0.1) : DesignSystem.Colors.textTertiary.opacity(0.1))
                    .frame(width: 50, height: 50)

                Text("\(count)")
                    .font(.system(size: 20, weight: .bold))
                    .foregroundColor(count >= minRequired ? DesignSystem.Colors.primary : DesignSystem.Colors.textSecondary)
            }

            Text(title)
                .font(.system(size: 14, weight: .medium))
                .foregroundColor(DesignSystem.Colors.textSecondary)
        }
    }

    /**
     * 报告类型选择器
     * 优化：改进视觉设计，增强交互反馈
     */
    private var reportTypeSelector: some View {
        VStack(spacing: 20) {
            HStack {
                Image(systemName: "doc.text.magnifyingglass")
                    .font(.system(size: 20))
                    .foregroundColor(DesignSystem.Colors.primary)

                Text("ai_analysis.select_report_type".localized)
                    .font(.system(size: 18, weight: .bold))
                    .foregroundColor(DesignSystem.Colors.textPrimary)

                Spacer()
            }

            VStack(spacing: 16) {
                // 行为分析报告选项
                reportTypeOption(
                    type: .behaviorAnalysis,
                    title: "行为分析报告",
                    subtitle: "基于积分记录分析行为趋势",
                    icon: "chart.line.uptrend.xyaxis"
                )

                // 成长报告选项
                reportTypeOption(
                    type: .growthReport,
                    title: "成长报告",
                    subtitle: "基于成长日记生成教育建议",
                    icon: "heart.text.square"
                )
            }
        }
        .padding(24)
        .background(
            RoundedRectangle(cornerRadius: 20)
                .fill(
                    LinearGradient(
                        gradient: Gradient(colors: [
                            Color.white,
                            Color(hex: "#fcfff4").opacity(0.3)
                        ]),
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
                .shadow(color: DesignSystem.Colors.primary.opacity(0.1), radius: 10, x: 0, y: 3)
                .shadow(color: .black.opacity(0.05), radius: 2, x: 0, y: 1)
        )
        .opacity(pageAppeared ? 1.0 : 0.0)
        .scaleEffect(pageAppeared ? 1.0 : 0.95)
        .animation(.spring(response: 0.6, dampingFraction: 0.8).delay(0.4), value: pageAppeared)
    }

    /**
     * 报告类型选项
     * 优化：增强视觉反馈，改进选中状态显示
     */
    private func reportTypeOption(type: AIReportType, title: String, subtitle: String, icon: String) -> some View {
        Button(action: {
            withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
                selectedReportType = type
            }
        }) {
            HStack(spacing: 16) {
                // 图标容器 - 增强视觉效果
                ZStack {
                    Circle()
                        .fill(selectedReportType == type ? DesignSystem.Colors.primary.opacity(0.2) : DesignSystem.Colors.textTertiary.opacity(0.1))
                        .frame(width: 44, height: 44)

                    Image(systemName: icon)
                        .font(.system(size: 20, weight: .medium))
                        .foregroundColor(selectedReportType == type ? DesignSystem.Colors.primary : DesignSystem.Colors.textSecondary)
                }

                // 文本信息
                VStack(alignment: .leading, spacing: 6) {
                    HStack {
                        Text(title)
                            .font(.system(size: 16, weight: .semibold))
                            .foregroundColor(DesignSystem.Colors.textPrimary)

                        Spacer()

                        // 选择指示器
                        Image(systemName: selectedReportType == type ? "checkmark.circle.fill" : "circle")
                            .font(.system(size: 18))
                            .foregroundColor(selectedReportType == type ? DesignSystem.Colors.primary : DesignSystem.Colors.textTertiary)
                    }

                    Text(subtitle)
                        .font(.system(size: 14))
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                        .multilineTextAlignment(.leading)
                }
            }
            .padding(18)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(
                        selectedReportType == type ?
                        LinearGradient(
                            gradient: Gradient(colors: [
                                DesignSystem.Colors.primary.opacity(0.1),
                                DesignSystem.Colors.primary.opacity(0.05)
                            ]),
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        ) :
                        LinearGradient(
                            gradient: Gradient(colors: [
                                Color(.systemGray6),
                                Color(.systemGray6)
                            ]),
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
            )
            .overlay(
                RoundedRectangle(cornerRadius: 16)
                    .stroke(
                        selectedReportType == type ? DesignSystem.Colors.primary.opacity(0.6) : Color.clear,
                        lineWidth: 2
                    )
            )
            .scaleEffect(selectedReportType == type ? 1.02 : 1.0)
            .shadow(
                color: selectedReportType == type ? DesignSystem.Colors.primary.opacity(0.2) : .clear,
                radius: selectedReportType == type ? 8 : 0,
                x: 0,
                y: selectedReportType == type ? 4 : 0
            )
        }
        .buttonStyle(PlainButtonStyle())
        .animation(.spring(response: 0.4, dampingFraction: 0.8), value: selectedReportType)
    }

    /**
     * 加载视图
     * 优化：增强加载动画效果，改进视觉反馈
     */
    private var loadingView: some View {
        VStack(spacing: 32) {
            Spacer()

            // 加载动画容器
            VStack(spacing: 24) {
                // 自定义加载动画
                ZStack {
                    // 外圈旋转动画
                    Circle()
                        .stroke(DesignSystem.Colors.primary.opacity(0.3), lineWidth: 4)
                        .frame(width: 80, height: 80)

                    Circle()
                        .trim(from: 0, to: 0.7)
                        .stroke(
                            LinearGradient(
                                gradient: Gradient(colors: [
                                    DesignSystem.Colors.primary,
                                    Color(hex: "#74c07f")
                                ]),
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            ),
                            style: StrokeStyle(lineWidth: 4, lineCap: .round)
                        )
                        .frame(width: 80, height: 80)
                        .rotationEffect(.degrees(pageAppeared ? 360 : 0))
                        .animation(.linear(duration: 2).repeatForever(autoreverses: false), value: pageAppeared)

                    // 中心图标
                    Image(systemName: "brain.head.profile")
                        .font(.system(size: 28, weight: .medium))
                        .foregroundColor(DesignSystem.Colors.primary)
                        .scaleEffect(pageAppeared ? 1.1 : 0.9)
                        .animation(.easeInOut(duration: 1).repeatForever(autoreverses: true), value: pageAppeared)
                }

                VStack(spacing: 12) {
                    Text("ai_analysis.generating".localized)
                        .font(.system(size: 18, weight: .semibold))
                        .foregroundColor(DesignSystem.Colors.textPrimary)

                    Text("ai_analysis.please_wait".localized)
                        .font(.system(size: 15))
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                        .multilineTextAlignment(.center)
                        .padding(.horizontal, 32)
                }
            }
            .padding(32)
            .background(
                RoundedRectangle(cornerRadius: 24)
                    .fill(
                        LinearGradient(
                            gradient: Gradient(colors: [
                                Color.white,
                                Color(hex: "#fcfff4").opacity(0.5)
                            ]),
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .shadow(color: DesignSystem.Colors.primary.opacity(0.1), radius: 20, x: 0, y: 8)
                    .shadow(color: .black.opacity(0.05), radius: 4, x: 0, y: 2)
            )

            Spacer()
        }
        .padding(.horizontal, 32)
    }

    /**
     * 报告内容视图
     */
    private func reportContentView(report: AIAnalysisReport) -> some View {
        // 报告详细内容 - 全屏显示，AIReportDetailView内部已有动画效果
        AIReportDetailView(report: report)
    }

    /**
     * 历史分析记录按钮
     */
    private var historyReportsSection: some View {
        VStack(spacing: 16) {
            // 分隔线
            Rectangle()
                .fill(DesignSystem.Colors.textTertiary.opacity(0.3))
                .frame(height: 1)
                .padding(.horizontal, 20)

            // 历史分析记录按钮 - 优化设计
            Button(action: {
                viewModel.showingHistoryReports = true
            }) {
                HStack(spacing: 16) {
                    // 图标容器
                    ZStack {
                        Circle()
                            .fill(
                                LinearGradient(
                                    gradient: Gradient(colors: [
                                        Color.blue.opacity(0.1),
                                        Color.blue.opacity(0.05)
                                    ]),
                                    startPoint: .topLeading,
                                    endPoint: .bottomTrailing
                                )
                            )
                            .frame(width: 40, height: 40)

                        Image(systemName: "doc.text.magnifyingglass")
                            .font(.system(size: 18, weight: .medium))
                            .foregroundColor(.blue)
                    }

                    // 文字
                    VStack(alignment: .leading, spacing: 4) {
                        Text("历史分析记录")
                            .font(.system(size: 16, weight: .semibold))
                            .foregroundColor(DesignSystem.Colors.textPrimary)

                        Text("查看所有AI分析报告")
                            .font(.system(size: 14))
                            .foregroundColor(DesignSystem.Colors.textSecondary)
                    }

                    Spacer()

                    // 箭头
                    Image(systemName: "chevron.right")
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(DesignSystem.Colors.textTertiary)
                }
                .padding(.horizontal, 20)
                .padding(.vertical, 18)
                .background(
                    RoundedRectangle(cornerRadius: 16)
                        .fill(
                            LinearGradient(
                                gradient: Gradient(colors: [
                                    Color.white,
                                    Color(hex: "#fcfff4").opacity(0.3)
                                ]),
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .shadow(color: .blue.opacity(0.1), radius: 8, x: 0, y: 3)
                        .shadow(color: .black.opacity(0.05), radius: 2, x: 0, y: 1)
                )
                .padding(.horizontal, 20)
            }
            .buttonStyle(PlainButtonStyle())

            Spacer(minLength: 20)
        }
        .background(Color.clear)
        .fullScreenCover(isPresented: $viewModel.showingHistoryReports) {
            AIAnalysisHistoryView(member: member)
        }
    }



    // MARK: - Methods

    /**
     * 设置视图
     */
    private func setupView() {
        viewModel.loadHistoryReports(for: member)

        withAnimation(.easeInOut(duration: 0.8)) {
            pageAppeared = true
        }
    }

    /**
     * 生成选中的报告
     */
    private func generateSelectedReport() async {
        switch selectedReportType {
        case .behaviorAnalysis:
            await viewModel.generateBehaviorAnalysisReport(for: member)
        case .growthReport:
            await viewModel.generateGrowthReport(for: member)
        }
    }

    /**
     * 获取权限状态描述
     */
    private func getPermissionStatusDescription() -> String {
        // 首先检查页面访问权限
        let pageAccessResult = viewModel.canAccessAIAnalysisPage(for: member)
        if !pageAccessResult.canAccess {
            return pageAccessResult.reason ?? "AI分析功能需要高级会员权限"
        }

        // 如果可以访问页面，检查生成报告权限
        let reportGenerationResult = viewModel.canGenerateReport(for: member, reportType: selectedReportType)
        return reportGenerationResult.reason ?? "AI分析功能需要高级会员权限"
    }


}
