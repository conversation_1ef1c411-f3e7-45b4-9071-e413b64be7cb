//
//  AIReportDetailViewTests.swift
//  ztt2Tests
//
//  Created by Augment Agent on 2025/8/1.
//

import XCTest
import SwiftUI
@testable import ztt2

/**
 * AI报告详情页面测试
 * 验证重构后的页面功能和视觉效果
 */
class AIReportDetailViewTests: XCTestCase {
    
    var testReport: AIAnalysisReport!
    
    override func setUp() {
        super.setUp()
        
        // 创建测试报告数据
        testReport = AIAnalysisReport(
            id: UUID(),
            reportType: .behaviorAnalysis,
            memberName: "测试成员",
            memberRole: "儿子",
            memberAge: 8,
            content: """
            # 8岁男孩成长分析报告
            
            ## 情绪变化分析
            
            根据积分记录分析，该成员在以下方面表现良好：
            
            - **积极情绪触发点**：学业成就感
            - **情绪调节能力**：逐步提升
            - **社交互动**：表现活跃
            
            ### 建议措施
            
            1. 继续鼓励学习积极性
            2. 培养情绪管理技能
            3. 增加社交活动机会
            
            这是一个包含markdown格式的测试报告内容。
            """,
            createdAt: Date()
        )
    }
    
    override func tearDown() {
        testReport = nil
        super.tearDown()
    }
    
    // MARK: - 基础功能测试
    
    /**
     * 测试页面基本渲染
     */
    func testBasicRendering() {
        let view = AIReportDetailView(report: testReport)
        
        // 验证视图可以正常创建
        XCTAssertNotNil(view)
        
        // 验证报告数据正确传递
        XCTAssertEqual(view.report.memberName, "测试成员")
        XCTAssertEqual(view.report.memberRole, "儿子")
        XCTAssertEqual(view.report.memberAge, 8)
        XCTAssertEqual(view.report.reportType, .behaviorAnalysis)
    }
    
    /**
     * 测试Markdown内容解析
     */
    func testMarkdownParsing() {
        let markdownText = MarkdownText(content: testReport.content)
        
        // 验证MarkdownText可以正常创建
        XCTAssertNotNil(markdownText)
        XCTAssertEqual(markdownText.content, testReport.content)
        
        // 验证内容不为空
        XCTAssertFalse(testReport.content.isEmpty, "测试内容不应为空")
        XCTAssertTrue(testReport.content.contains("# "), "应该包含一级标题")
        XCTAssertTrue(testReport.content.contains("## "), "应该包含二级标题")
        XCTAssertTrue(testReport.content.contains("### "), "应该包含三级标题")
        XCTAssertTrue(testReport.content.contains("- "), "应该包含列表项")
        XCTAssertTrue(testReport.content.contains("**"), "应该包含粗体文本")
    }
    
    /**
     * 测试报告类型图标
     */
    func testReportTypeIcon() {
        let behaviorReport = AIAnalysisReport(
            id: UUID(),
            reportType: .behaviorAnalysis,
            memberName: "测试",
            memberRole: "测试",
            memberAge: 8,
            content: "测试内容",
            createdAt: Date()
        )
        
        let growthReport = AIAnalysisReport(
            id: UUID(),
            reportType: .growthReport,
            memberName: "测试",
            memberRole: "测试",
            memberAge: 8,
            content: "测试内容",
            createdAt: Date()
        )
        
        // 验证不同报告类型
        XCTAssertEqual(behaviorReport.reportType, .behaviorAnalysis)
        XCTAssertEqual(growthReport.reportType, .growthReport)
        
        // 验证报告类型显示名称
        XCTAssertEqual(behaviorReport.reportType.displayName, "行为分析报告")
        XCTAssertEqual(growthReport.reportType.displayName, "成长分析报告")
    }
    
    /**
     * 测试日期格式化
     */
    func testDateFormatting() {
        let testDate = Date()
        
        // 测试完整日期时间格式
        let fullDateTime = DateFormatter.fullDateTime.string(from: testDate)
        XCTAssertFalse(fullDateTime.isEmpty, "完整日期时间格式不应为空")
        
        // 测试简短日期时间格式
        let shortDateTime = DateFormatter.shortDateTime.string(from: testDate)
        XCTAssertFalse(shortDateTime.isEmpty, "简短日期时间格式不应为空")
        
        // 验证格式化器的本地化设置
        XCTAssertEqual(DateFormatter.fullDateTime.locale?.identifier, "zh_CN")
        XCTAssertEqual(DateFormatter.shortDateTime.locale?.identifier, "zh_CN")
    }
    
    /**
     * 测试Toast视图
     */
    func testToastView() {
        let toastMessage = "测试消息"
        let toastView = ToastView(message: toastMessage)
        
        // 验证Toast视图可以正常创建
        XCTAssertNotNil(toastView)
        XCTAssertEqual(toastView.message, toastMessage)
    }
    
    /**
     * 测试分享功能
     */
    func testShareSheet() {
        let shareItems = ["测试分享内容"]
        let shareSheet = ShareSheet(items: shareItems)
        
        // 验证分享组件可以正常创建
        XCTAssertNotNil(shareSheet)
        XCTAssertEqual(shareSheet.items.count, 1)
    }
    
    // MARK: - 性能测试
    
    /**
     * 测试大量内容的处理性能
     */
    func testLargeContentPerformance() {
        // 创建大量内容的测试报告
        let largeContent = String(repeating: "这是一段测试内容。\n", count: 100)
        let largeReport = AIAnalysisReport(
            id: UUID(),
            reportType: .behaviorAnalysis,
            memberName: "测试成员",
            memberRole: "儿子",
            memberAge: 8,
            content: largeContent,
            createdAt: Date()
        )
        
        // 测试视图创建性能
        measure {
            let view = AIReportDetailView(report: largeReport)
            let markdownText = MarkdownText(content: largeReport.content)
            // 验证可以正常创建
            XCTAssertNotNil(view)
            XCTAssertNotNil(markdownText)
        }
    }
    
    /**
     * 测试内存使用
     */
    func testMemoryUsage() {
        // 创建多个视图实例
        var views: [AIReportDetailView] = []
        
        for i in 0..<10 {
            let report = AIAnalysisReport(
                id: UUID(),
                reportType: .behaviorAnalysis,
                memberName: "测试成员\(i)",
                memberRole: "儿子",
                memberAge: 8,
                content: "测试内容\(i)",
                createdAt: Date()
            )
            views.append(AIReportDetailView(report: report))
        }
        
        // 验证所有视图都能正常创建
        XCTAssertEqual(views.count, 10)
        
        // 清理内存
        views.removeAll()
        XCTAssertEqual(views.count, 0)
    }
    
    // MARK: - 边界条件测试
    
    /**
     * 测试空内容处理
     */
    func testEmptyContent() {
        let emptyReport = AIAnalysisReport(
            id: UUID(),
            reportType: .behaviorAnalysis,
            memberName: "测试成员",
            memberRole: "儿子",
            memberAge: 8,
            content: "",
            createdAt: Date()
        )
        
        let view = AIReportDetailView(report: emptyReport)
        let markdownText = MarkdownText(content: emptyReport.content)
        
        // 验证空内容不会导致崩溃
        XCTAssertNotNil(view)
        XCTAssertNotNil(markdownText)
        XCTAssertEqual(markdownText.content, "")
    }
    
    /**
     * 测试特殊字符处理
     */
    func testSpecialCharacters() {
        let specialContent = """
        # 特殊字符测试 🎉
        
        ## 包含emoji和特殊符号 ⭐️
        
        - **测试内容**：包含中文、English、123数字
        - 特殊符号：@#$%^&*()
        - Unicode字符：αβγδε
        
        这是一个包含各种特殊字符的测试。
        """
        
        let specialReport = AIAnalysisReport(
            id: UUID(),
            reportType: .behaviorAnalysis,
            memberName: "测试成员",
            memberRole: "儿子",
            memberAge: 8,
            content: specialContent,
            createdAt: Date()
        )
        
        let view = AIReportDetailView(report: specialReport)
        let markdownText = MarkdownText(content: specialReport.content)
        
        // 验证特殊字符不会导致问题
        XCTAssertNotNil(view)
        XCTAssertNotNil(markdownText)
        XCTAssertTrue(specialReport.content.contains("🎉"))
        XCTAssertTrue(specialReport.content.contains("⭐️"))
    }
}
