//
//  AIAnalysisService.swift
//  ztt1
//
//  Created by AI Assistant on 2025/1/17.
//

import Foundation
import Network

/**
 * AI分析服务
 * 负责DeepSeek API调用、数据脱敏处理和权限验证
 */
@MainActor
class AIAnalysisService: ObservableObject {
    
    // MARK: - Properties
    
    private let apiKey = "sk-cf36ddf64e8d49249d911a6e16016915"
    private let baseURL = "https://api.deepseek.com"
    private let model = "deepseek-chat"
    
    // 网络监控
    private let networkMonitor = NWPathMonitor()
    private let networkQueue = DispatchQueue(label: "NetworkMonitor")
    @Published var isNetworkAvailable = true
    
    // 请求去重机制
    private var isGenerating = false
    private let requestQueue = DispatchQueue(label: "AIAnalysisService.request", qos: .userInitiated)
    
    // 专用URLSession配置
    private lazy var urlSession: URLSession = {
        let config = URLSessionConfiguration.default
        
        // 超时配置
        config.timeoutIntervalForRequest = 90.0      // 请求超时90秒
        config.timeoutIntervalForResource = 120.0    // 资源超时120秒
        
        // 网络连接策略
        config.waitsForConnectivity = true           // 等待网络连接
        config.allowsCellularAccess = true           // 允许蜂窝网络
        config.allowsExpensiveNetworkAccess = true   // 允许昂贵网络（如漫游）
        config.allowsConstrainedNetworkAccess = true // 允许受限网络
        config.networkServiceType = .responsiveData  // 响应数据服务类型
        
        // HTTP配置
        config.httpMaximumConnectionsPerHost = 2     // 每个主机最大连接数
        config.httpShouldUsePipelining = false       // 禁用HTTP管道
        config.httpShouldSetCookies = false          // 禁用Cookie
        config.requestCachePolicy = .reloadIgnoringLocalCacheData // 忽略缓存
        
        // TLS/SSL配置
        config.tlsMinimumSupportedProtocolVersion = .TLSv12
        config.tlsMaximumSupportedProtocolVersion = .TLSv13
        
        // 创建URLSession
        let session = URLSession(configuration: config)
        
        print("🔧 URLSession配置完成:")
        print("   - 请求超时: \(config.timeoutIntervalForRequest)秒")
        print("   - 资源超时: \(config.timeoutIntervalForResource)秒")
        print("   - 等待连接: \(config.waitsForConnectivity)")
        print("   - 蜂窝网络: \(config.allowsCellularAccess)")
        print("   - TLS版本: v1.2-v1.3")
        
        return session
    }()
    
    // MARK: - Initialization
    
    init() {
        startNetworkMonitoring()
    }
    
    deinit {
        networkMonitor.cancel()
    }
    
    // MARK: - Public Methods
    
    /**
     * 验证用户权限
     * 双重验证：会员等级 + 记录数量
     */
    func validatePermissions(for user: User?, student: Student) -> AIAnalysisPermissionResult {
        // 检查用户是否存在
        guard let user = user else {
            return .denied(reason: "ai_analysis.error.no_user".localized)
        }
        
        // 检查会员等级
        guard user.isAdvancedUser else {
            return .denied(
                reason: "ai_analysis.error.not_premium".localized,
                canUpgrade: true
            )
        }
        
        // 检查积分记录数量
        let validRecords = student.sortedPointRecords.filter { !$0.isReversed }
        guard validRecords.count >= 10 else {
            return .denied(
                reason: String(format: "ai_analysis.error.insufficient_records".localized, validRecords.count)
            )
        }
        
        // 检查网络连接
        guard isNetworkAvailable else {
            return .denied(reason: "ai_analysis.error.no_network".localized)
        }
        
        return .allowed()
    }
    
    /**
     * 准备分析数据（脱敏处理）
     */
    func prepareAnalysisData(from student: Student) -> StudentAnalysisData? {
        return StudentAnalysisData.from(student: student)
    }
    
    /**
     * 生成AI分析报告
     * @param data 学生分析数据
     * @param reportType 报告类型，默认为专业分析报告
     */
    func generateAnalysisReport(data: StudentAnalysisData, reportType: ReportType = .professional) async throws -> AIAnalysisReport {
        // 检查是否正在生成
        if isGenerating {
            print("⚠️ AI分析请求被阻止：已有请求正在进行中")
            throw AIAnalysisError.unknownError("正在生成报告中，请稍候")
        }

        isGenerating = true
        print("🚀 开始AI分析请求，报告类型: \(reportType)")

        do {
            let report = try await performAPIRequest(data: data, reportType: reportType)
            isGenerating = false
            return report
        } catch {
            isGenerating = false
            throw error
        }
    }
    
    /**
     * 执行实际的API请求
     */
    private func performAPIRequest(data: StudentAnalysisData, reportType: ReportType) async throws -> AIAnalysisReport {
        // 验证网络连接
        guard isNetworkAvailable else {
            throw AIAnalysisError.networkError(NSError(domain: "NetworkUnavailable", code: -1))
        }
        
        print("📡 准备发送API请求...")
        
        // 执行网络连通性诊断
        await performNetworkDiagnostics()
        
        // 构建请求
        let request = try buildAPIRequest(data: data, reportType: reportType)
        
        // 添加重试机制
        var lastError: Error?
        let maxRetries = 2  // 最多重试2次
        
        for attempt in 1...maxRetries {
            do {
                print("🔄 第\(attempt)次尝试发送请求...")
                
                // 发送请求
                let (responseData, response) = try await urlSession.data(for: request)
                
                print("📥 收到API响应")
                
                // 处理HTTP响应
                guard let httpResponse = response as? HTTPURLResponse else {
                    print("❌ 无效的HTTP响应")
                    throw AIAnalysisError.networkError(NSError(domain: "InvalidResponse", code: -1))
                }
                
                print("🔍 HTTP状态码: \(httpResponse.statusCode)")
                
                // 处理不同的HTTP状态码
                switch httpResponse.statusCode {
                case 200:
                    // 成功响应，继续处理
                    break
                case 400:
                    print("❌ API请求参数错误 (400)")
                    _ = parseErrorResponse(responseData)
                    throw AIAnalysisError.apiError(statusCode: 400, message: "请求参数有误，请稍后重试")
                case 401:
                    print("❌ API密钥无效 (401)")
                    throw AIAnalysisError.apiError(statusCode: 401, message: "服务认证失败，请联系客服")
                case 429:
                    print("❌ API请求频率限制 (429)")
                    // 429错误需要重试
                    if attempt < maxRetries {
                        print("⏱️ 请求频率限制，等待后重试...")
                        try await Task.sleep(nanoseconds: 3_000_000_000) // 等待3秒
                        continue
                    }
                    throw AIAnalysisError.apiError(statusCode: 429, message: "请求过于频繁，请稍后重试")
                case 500, 502, 503:
                    print("❌ API服务器错误 (\(httpResponse.statusCode))")
                    // 服务器错误需要重试
                    if attempt < maxRetries {
                        print("⏱️ 服务器暂时不可用，等待后重试...")
                        try await Task.sleep(nanoseconds: 5_000_000_000) // 等待5秒
                        continue
                    }
                    _ = parseErrorResponse(responseData)
                    throw AIAnalysisError.apiError(statusCode: httpResponse.statusCode, message: "服务暂时不可用，请稍后重试")
                default:
                    print("❌ 未知HTTP错误: \(httpResponse.statusCode)")
                    _ = parseErrorResponse(responseData)
                    throw AIAnalysisError.apiError(statusCode: httpResponse.statusCode, message: "网络服务异常，请稍后重试")
                }
                
                // 解析响应
                let analysisContent = try parseAPIResponse(responseData)
                
                // 创建分析报告
                let report = AIAnalysisReport(
                    studentName: data.studentName,
                    studentGender: data.gender,
                    gradeLevel: data.gradeLevel,
                    statistics: data.statistics,
                    analysisContent: analysisContent,
                    reportType: reportType
                )
                
                print("✅ AI分析报告生成成功")
                return report
                
            } catch let error as AIAnalysisError {
                // 重新抛出已处理的错误（不重试）
                throw error
            } catch {
                lastError = error
                print("❌ 第\(attempt)次请求失败: \(error.localizedDescription)")
                
                // 详细错误分析
                if let nsError = error as NSError? {
                    print("🔍 错误详情:")
                    print("   Domain: \(nsError.domain)")
                    print("   Code: \(nsError.code)")
                    print("   Description: \(nsError.localizedDescription)")
                    print("   UserInfo: \(nsError.userInfo)")
                }
                
                // 检查是否是超时错误，如果是且还有重试机会，则重试
                if let nsError = error as NSError?, 
                   nsError.domain == NSURLErrorDomain && nsError.code == NSURLErrorTimedOut {
                    if attempt < maxRetries {
                        print("⏱️ 网络超时，等待后重试...")
                        try await Task.sleep(nanoseconds: 2_000_000_000) // 等待2秒
                        continue
                    }
                }
                
                // 如果是最后一次尝试或者不是可重试的错误，则抛出
                if attempt == maxRetries {
                    print("❌ 所有重试都失败了")
                    if error.localizedDescription.contains("timeout") || error.localizedDescription.contains("超时") {
                        throw AIAnalysisError.networkError(NSError(domain: "RequestTimeout", code: -1))
                    } else {
                        throw AIAnalysisError.networkError(error)
                    }
                }
            }
        }
        
        // 理论上不会执行到这里，但为了安全性
        throw AIAnalysisError.networkError(lastError ?? NSError(domain: "UnknownError", code: -1))
    }
    
    /**
     * 执行网络连通性诊断
     */
    private func performNetworkDiagnostics() async {
        print("🔧 开始网络诊断...")
        
        // 测试基本网络连接
        do {
            let testURL = URL(string: "https://www.apple.com")!
            let (_, response) = try await URLSession.shared.data(from: testURL)
            if let httpResponse = response as? HTTPURLResponse {
                print("✅ 基本网络连接正常 (Apple): \(httpResponse.statusCode)")
            }
        } catch {
            print("❌ 基本网络连接失败: \(error.localizedDescription)")
        }
        
        // 测试DeepSeek API连通性
        do {
            let deepseekURL = URL(string: "\(baseURL)")!
            let (_, response) = try await URLSession.shared.data(from: deepseekURL)
            if let httpResponse = response as? HTTPURLResponse {
                print("✅ DeepSeek API域名可达: \(httpResponse.statusCode)")
            }
        } catch {
            print("❌ DeepSeek API连接失败: \(error.localizedDescription)")
            
            // 如果连接失败，提供更多诊断信息
            if let nsError = error as NSError? {
                switch nsError.code {
                case NSURLErrorNotConnectedToInternet:
                    print("🔍 诊断：设备未连接到互联网")
                case NSURLErrorTimedOut:
                    print("🔍 诊断：连接超时，可能是网络环境问题")
                case NSURLErrorCannotFindHost:
                    print("🔍 诊断：无法找到主机，可能是DNS问题")
                case NSURLErrorCannotConnectToHost:
                    print("🔍 诊断：无法连接到主机，可能需要代理")
                case NSURLErrorSecureConnectionFailed:
                    print("🔍 诊断：SSL/TLS连接失败")
                default:
                    print("🔍 诊断：未知网络错误 (Code: \(nsError.code))")
                }
            }
        }
        
        print("🔧 网络诊断完成")
    }
    
    /**
     * 测试API密钥有效性
     */
    private func testAPIKey() async {
        print("🔑 测试API密钥有效性...")
        
        do {
            // 构建一个简单的测试请求
            guard let url = URL(string: "\(baseURL)/chat/completions") else {
                print("❌ API URL无效")
                return
            }
            
            var request = URLRequest(url: url)
            request.httpMethod = "POST"
            request.setValue("application/json", forHTTPHeaderField: "Content-Type")
            request.setValue("Bearer \(apiKey)", forHTTPHeaderField: "Authorization")
            request.setValue("DeepSeek-iOS-Client/1.0", forHTTPHeaderField: "User-Agent")
            request.timeoutInterval = 30.0
            
            // 构建最简单的测试请求体
            let testRequestBody: [String: Any] = [
                "model": model,
                "messages": [
                    [
                        "role": "user",
                        "content": "测试"
                    ]
                ],
                "max_tokens": 10
            ]
            
            request.httpBody = try JSONSerialization.data(withJSONObject: testRequestBody)
            
            let (responseData, response) = try await URLSession.shared.data(for: request)
            
            if let httpResponse = response as? HTTPURLResponse {
                switch httpResponse.statusCode {
                case 200:
                    print("✅ API密钥验证成功")
                case 401:
                    print("❌ API密钥无效或已过期")
                case 429:
                    print("⚠️ API请求频率限制，但密钥有效")
                case 402:
                    print("❌ API账户余额不足")
                default:
                    print("❓ API返回状态码: \(httpResponse.statusCode)")
                    if let responseString = String(data: responseData, encoding: .utf8) {
                        print("📄 响应内容: \(responseString)")
                    }
                }
            }
            
        } catch {
            print("❌ API密钥测试失败: \(error.localizedDescription)")
            
            if let nsError = error as NSError? {
                print("🔍 API测试错误详情:")
                print("   Domain: \(nsError.domain)")
                print("   Code: \(nsError.code)")
                
                if nsError.domain == NSURLErrorDomain && nsError.code == NSURLErrorTimedOut {
                    print("🔍 特别注意：API请求超时，可能存在网络连接问题")
                }
            }
        }
    }
    
    /**
     * 检查网络连接状态
     */
    func checkNetworkConnection() -> Bool {
        return isNetworkAvailable
    }
    
    // MARK: - Private Methods
    
    /**
     * 开始网络监控
     */
    private func startNetworkMonitoring() {
        networkMonitor.pathUpdateHandler = { [weak self] path in
            DispatchQueue.main.async {
                self?.isNetworkAvailable = path.status == .satisfied
            }
        }
        networkMonitor.start(queue: networkQueue)
    }
    
    /**
     * 构建API请求
     */
    private func buildAPIRequest(data: StudentAnalysisData, reportType: ReportType) throws -> URLRequest {
        guard let url = URL(string: "\(baseURL)/chat/completions") else {
            throw AIAnalysisError.unknownError("Invalid API URL")
        }
        
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        request.setValue("Bearer \(apiKey)", forHTTPHeaderField: "Authorization")
        request.setValue("DeepSeek-iOS-Client/1.0", forHTTPHeaderField: "User-Agent")
        request.timeoutInterval = 90.0  // 增加到90秒
        
        // 构建请求体
        let requestBody = buildRequestBody(data: data, reportType: reportType)
        
        // 打印请求大小用于调试
        let requestData = try JSONSerialization.data(withJSONObject: requestBody)
        print("📊 请求数据大小: \(requestData.count) bytes")
        
        request.httpBody = requestData
        
        return request
    }
    
    /**
     * 构建请求体
     * @param data 学生分析数据
     * @param reportType 报告类型
     */
    private func buildRequestBody(data: StudentAnalysisData, reportType: ReportType) -> [String: Any] {
        // 构建精简的数据描述
        let dataDescription = buildOptimizedDataDescription(data: data)
        
        // 获取当前语言
        let isEnglish = LocalizationManager.shared.currentLanguage == .english
        
        // 根据报告类型和语言选择不同的提示词
        var systemPrompt: String
        var userPrompt: String
        
        switch reportType {
        case .professional:
            // 学生分析报告提示词
            if isEnglish {
                // 英文提示词
                systemPrompt = """
                You are a professional child education behavior analyst. Please generate a complete student behavior analysis report based on the student's point records.
                
                Please pay special attention to:
                - The analysis report should be suitable for teachers and parents to read, with concise language and clear structure.
                - Please prioritize analyzing behavior trends, such as whether certain behaviors are improving or recurring.
                - Based on the chronological order in the records, analyze the student's recent performance trajectory.
                - Please identify the student's strengths and areas for improvement, list them separately and give concise explanations.
                - If the records include positive behaviors (such as helping classmates, listening attentively, etc.), please praise and encourage them.
                - If the records include negative behaviors (such as being late, disrupting class, etc.), please objectively point them out and briefly offer suggestions.
                - Please include brief family advice at the end of the report, suitable for parents to reference in daily education.
                - Please keep the entire report around 500 words, without titles or numbered paragraphs, in gentle but instructive language.
                - IMPORTANT: You MUST respond in English. Your entire response must be in English only.
                """
                
                userPrompt = "Here are the student's historical behavior records (in chronological order):\n\(dataDescription)\n\nIMPORTANT: Your response must be in English only."
            } else {
                // 中文提示词
                systemPrompt = """
                你是一位专业的儿童教育行为分析师，需要根据学生的加分与扣分记录，生成一份完整的学生行为分析报告。
                
                请特别注意：
                - 分析报告应适合面向老师和家长阅读，语言简洁、结构清晰。
                - 请优先分析行为的变化趋势，比如某类行为是否变好、是否反复出现等。
                - 请根据记录中的时间顺序，分析出学生近期表现的变化轨迹。
                - 请识别出学生的优点与需要改进的方面，分别列出并给予简洁解释。
                - 如果记录中包含积极行为（如帮助同学、认真听讲等），请予以表扬与鼓励。
                - 如果记录中包含负面行为（如迟到、打扰课堂等），请客观指出，并简要提出建议。
                - 请在报告末尾以简短语气给出家庭建议，适合家长在日常教育中参考。
                - 请保持整份报告在500字左右，不使用标题，不用分段编号，语言温和但具有指导性。
                - 重要：你必须使用中文回复。你的整个回答必须只用中文。
                """
                
                userPrompt = "以下是该学生的历史行为记录（按时间排序）：\n\(dataDescription)\n\n重要：你的回答必须只用中文。"
            }
            
        case .parentFeedback:
            // 家长反馈提示词
            if isEnglish {
                // 英文提示词
                systemPrompt = """
                You are the homeroom teacher of this student. Please communicate with the student's parents using a gentle, authentic, and caring tone, and generate an analysis report on the student's recent behavior performance with the following requirements:
                
                - This analysis is for the student's parents, please use a conversational tone rather than an official style.
                - Briefly describe the overall trend of the student's recent performance at school at the beginning, using phrases like "I've observed..." and "He/she has recently..."
                - The analysis should reference time trends in the records, for example: "There were several instances of tardiness last week, but this has significantly improved recently."
                - Based on the chronological order in the records, analyze the student's recent performance trajectory.
                - Please identify the student's strengths, such as helping classmates, listening attentively, independent learning, etc., and give positive affirmation.
                - Also point out areas that need improvement, such as being distracted, forgetting homework, disrupting classmates, etc., using a gentle approach to remind and provide reasonable suggestions.
                - Please use age-appropriate language according to the student's grade and gender characteristics, avoiding overly adult-oriented or critical language.
                - Please add a warm suggestion at the end of the report, such as: "If you can continue to encourage her to complete homework independently at home, I believe she will become even better."
                - IMPORTANT: You MUST respond in English. Your entire response must be in English only.
                """
                
                userPrompt = """
                Student Basic Information:
                Gender: \(data.gender == "male" ? "Male" : "Female")
                Grade: \(data.gradeLevel)
                
                Here are the student's recent behavior records (in chronological order):
                \(dataDescription)
                
                Please generate a concise, natural, clearly structured behavior analysis report of about 300-500 words based on the above content. Do not use titles or numbers, and present the entire content as a conversational description with the parents. IMPORTANT: Your response must be in English only.
                """
            } else {
                // 中文提示词
                systemPrompt = """
                你现在是这位学生的班主任，请你用温和、真实、关切的语气，与学生家长进行沟通，生成一份关于学生近期行为表现的分析报告，内容要求如下：
                
                - 这份分析是面向学生家长的，请使用日常交流语气，而不是官方文体。
                - 开头简要说明学生近期在校表现整体趋势，可适度使用"我观察到……"、"他/她最近……"等语句。
                - 分析应参考记录的时间变化趋势，例如："上周出现过几次迟到的情况，但最近已明显改善"。
                - 根据记录中的时间顺序，分析出学生近期表现的变化轨迹。
                - 请识别出学生的优点，例如：帮助同学、认真听讲、自主学习等，给予积极肯定。
                - 也请指出需要改进的地方，如注意力分散、忘带作业、打扰同学等，用委婉方式提醒，并提出合理建议。
                - 请结合学生的年级与性别特征，采用适龄语言，不要太成人化或苛责。
                - 报告末尾请加一句温暖的建议语，例如："如果家里能继续鼓励她主动完成作业，相信她会越来越棒的。"
                - 重要：你必须使用中文回复。你的整个回答必须只用中文。
                """
                
                userPrompt = """
                学生基本信息：
                性别：\(data.gender == "male" ? "男" : "女")
                年级：\(data.gradeLevel)
                
                以下是该学生最近的行为记录（按时间排序）：
                \(dataDescription)
                
                请基于以上内容生成一份简洁自然、结构清晰、约300～500字的学生行为表现分析报告。不要使用标题，不要列编号，整份内容呈现为一段与家长对话式的描述。重要：你的回答必须只用中文。
                """
            }
        }
        
        return [
            "model": model,
            "messages": [
                [
                    "role": "system",
                    "content": systemPrompt
                ],
                [
                    "role": "user",
                    "content": userPrompt
                ]
            ],
            "stream": false,
            "temperature": 0.7,
            "max_tokens": 1000  // 限制输出长度
        ]
    }
    
    /**
     * 构建数据描述（根据用户要求显示所有积分记录）
     */
    private func buildOptimizedDataDescription(data: StudentAnalysisData) -> String {
        let statistics = data.statistics
        
        // 获取当前语言
        let isEnglish = LocalizationManager.shared.currentLanguage == .english
        
        // 精简的基本信息
        var description = isEnglish 
            ? """
            Student: \(data.gender == "male" ? "Male" : "Female"), \(data.gradeLevel)
            
            Statistics: Total \(statistics.totalRecords) records, \(statistics.positiveRecords) positive (\(statistics.totalPositivePoints) points), \(statistics.negativeRecords) negative (\(statistics.totalNegativePoints) points)
            
            All Point Records:
            """
            : """
            学生：\(data.gender == "male" ? "男" : "女")，\(data.gradeLevel)
            
            统计：总\(statistics.totalRecords)条，加分\(statistics.positiveRecords)次(\(statistics.totalPositivePoints)分)，扣分\(statistics.negativeRecords)次(\(statistics.totalNegativePoints)分)
            
            所有积分记录：
            """
        
        // 显示所有积分记录（按照日期格式化并标记是加分还是扣分）
        for record in data.pointRecords {
            let dateFormatter = DateFormatter()
            dateFormatter.dateFormat = "yyyy-MM-dd"
            let dateString = dateFormatter.string(from: record.timestamp)
            
            if isEnglish {
                let recordType = record.value > 0 ? "Add Points" : "Deduct Points"
                description += "\n\(dateString) \(recordType): \(record.reason)"
            } else {
                let recordType = record.value > 0 ? "加分" : "扣分"
                description += "\n\(dateString) \(recordType): \(record.reason)"
            }
        }
        
        return description
    }
    
    /**
     * 清理markdown内容，移除开头的markdown标记
     */
    private func cleanMarkdownContent(_ content: String) -> String {
        var cleanedContent = content.trimmingCharacters(in: .whitespacesAndNewlines)

        // 移除开头的```markdown标记
        if cleanedContent.hasPrefix("```markdown") {
            cleanedContent = String(cleanedContent.dropFirst(11))
        }

        // 移除开头的```标记
        if cleanedContent.hasPrefix("```") {
            cleanedContent = String(cleanedContent.dropFirst(3))
        }

        // 移除结尾的```标记
        if cleanedContent.hasSuffix("```") {
            cleanedContent = String(cleanedContent.dropLast(3))
        }

        return cleanedContent.trimmingCharacters(in: .whitespacesAndNewlines)
    }

    /**
     * 解析API响应
     */
    private func parseAPIResponse(_ data: Data) throws -> String {
        do {
            guard let jsonObject = try JSONSerialization.jsonObject(with: data) as? [String: Any],
                  let choices = jsonObject["choices"] as? [[String: Any]],
                  let firstChoice = choices.first,
                  let message = firstChoice["message"] as? [String: Any],
                  let content = message["content"] as? String else {
                throw AIAnalysisError.parseError(NSError(domain: "InvalidJSONStructure", code: -1))
            }
            
            // 清理和格式化内容
            let cleanContent = cleanMarkdownContent(content)
            guard !cleanContent.isEmpty else {
                throw AIAnalysisError.parseError(NSError(domain: "EmptyContent", code: -1))
            }

            return cleanContent
        } catch {
            throw AIAnalysisError.parseError(error)
        }
    }
    
    /**
     * 解析错误响应信息（用于调试）
     */
    private func parseErrorResponse(_ data: Data) -> String {
        guard let errorString = String(data: data, encoding: .utf8) else {
            return "未知错误响应"
        }
        
        print("🔍 API错误响应: \(errorString)")
        
        // 尝试解析JSON错误信息
        if let jsonData = try? JSONSerialization.jsonObject(with: data) as? [String: Any],
           let error = jsonData["error"] as? [String: Any],
           let message = error["message"] as? String {
            return message
        }
        
        return errorString
    }
}

// MARK: - Network Connectivity Extension

extension AIAnalysisService {
    
    /**
     * 手动检查网络连接
     */
    func performNetworkCheck() async -> Bool {
        guard let url = URL(string: "https://www.apple.com") else { return false }
        
        do {
            let (_, response) = try await URLSession.shared.data(from: url)
            if let httpResponse = response as? HTTPURLResponse {
                return httpResponse.statusCode == 200
            }
            return false
        } catch {
            return false
        }
    }
}